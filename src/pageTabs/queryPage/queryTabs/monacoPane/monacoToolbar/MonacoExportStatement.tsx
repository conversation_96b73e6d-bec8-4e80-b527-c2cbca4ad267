import React, { useCallback, useState } from 'react'
import { Col, Space, Tooltip, message } from 'antd'
import { Iconfont } from 'src/components'
import { getOperatingObject, getSelectedText } from 'src/util'
import { 
  makeResultAllExport, 
  getSelectedStatementResult,
 } from 'src/api'
import { useRequest, useSelector } from 'src/hook'
import { activeMonacoPaneInfoSelector } from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'
import { useEditorInstance } from 'src/components/BaseEditor/useEditorInstance'
import { ResultAllExport } from 'src/pageTabs/queryPage/resultTabs/resultContentGrid/ResultAllExport'
import styles from './index.module.scss';
import classNames from 'classnames'
import { useTranslation } from 'react-i18next'
import { exportTaskCreatedNot } from 'src/components/ExportNotification'

export const MonacoExportStatement = React.memo(() => { 
  const { t } = useTranslation()
  const activePaneInfo = useSelector(activeMonacoPaneInfoSelector)
 
  const [editorInstance] = useEditorInstance();
  const selectedStatement = editorInstance && getSelectedText(editorInstance);
  const [visibleExportAll, setVisibleExportAll] = useState(false)

  //获取语句执行结果
  const { data: exportPermission, run: getExportPermission } = useRequest(() => {
    if (!activePaneInfo) return
    const { key, plSql, tSql, connectionId, connectionType, databaseName, schemaName } = activePaneInfo;
    return selectedStatement && connectionType && getSelectedStatementResult({
      connectionId,
      dataSourceType: connectionType,
      operatingObject: getOperatingObject(
        { databaseName, schemaName },
        connectionType,
      ),
      offset: 0,
      rowCount: 100,
      databaseName,
      tabKey: key,
      plSql,
      tSql,
      autoCommit: activePaneInfo?.txMode === 'auto',
      statements: [
        selectedStatement
      ],
    })
  }, {
    manual: true,
    formatResult: (res: any) => {
      return res?.executionInfos?.[0]?.response
    },
    refreshDeps: [selectedStatement, activePaneInfo?.key]
  });

  const handleExportAllResult = useCallback(
    (data: any) => {
      const params = {
        connectionId: activePaneInfo?.connectionId,
        connectionType: activePaneInfo?.connectionType,
        databaseName: activePaneInfo?.databaseName,

        operatingObject: activePaneInfo && getOperatingObject(
          activePaneInfo,
          //@ts-ignore
          activePaneInfo?.connectionType,
        ),
        statement: editorInstance && getSelectedText(editorInstance),
        containTempTable: false,
        tabKey: activePaneInfo?.key,
      }
      return makeResultAllExport(Object.assign({}, params, data))
      .then(() => {
        exportTaskCreatedNot()
      })

    },
    [activePaneInfo],
  )

  return (
    <Col>
      <Space size={1}>
        <Tooltip title={t('sdo_export_all')} placement='bottom'>
          <Iconfont
            type="icon-xiazai"
            className={classNames(styles.monacoToolbarIcon)}
            onClick={async () => {
              if (!editorInstance || !getSelectedText(editorInstance)) {
                return message.error(t('sdo_select_export_statement'))
              }

              const permRes = await getExportPermission();
              if(!permRes?.permissionResult?.exportDefault || !permRes?.dataExport?.status) {
                return message.error(t('sdo_no_export_permission'))
              }
              
              if (permRes?.permissionResult?.exportDefault && permRes?.dataExport?.status) {
                setVisibleExportAll(true)
              }
            }}
          />
        </Tooltip>
      </Space>
      <ResultAllExport
        isMultiStatementExport={true}
        result={{ ...(exportPermission || {}), statement: editorInstance && getSelectedText(editorInstance) }}
        visible={visibleExportAll}
        setVisible={setVisibleExportAll}
        hanldeExportAll={handleExportAllResult}
        permissionResult={exportPermission?.permissionResult || {}}
      />
    </Col>
  )
})