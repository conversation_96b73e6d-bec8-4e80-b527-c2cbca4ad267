import React, { useEffect, useState } from "react";
import _ from 'lodash';
import { useTranslation } from 'react-i18next';
import type { TableRowSelection } from "antd/es/table/interface"
import { Button, Table, Space, Input, Select, message } from 'antd';
import { ErrorBoundary } from 'src/components';
import { useRequest, useSelector } from 'src/hook'
import {
  ClassResColumnAPI,
  getClassDatasourceAPI,
  IClassTaskExecuteTaskParams
} from 'src/api';
import { ClassificationRes } from '../../types';
import { CLASSIFICATION_RES } from '../../constants';
import ExportModal from '../../common/CommonExportModal'
import { getTablePaginatinLimitFiled, generateKeyValueObjects } from '../../utils';
import columns from './columns';
import styles from './index.module.scss';

interface TableParams {
  pageNum: number;
  pageSize: number;
  type: string;
  search?: string;
  level_name?: string;
}

const ColumnResultTab: React.FC = () => {
  const defaultTableParams: TableParams = {
    pageNum: 1,
    pageSize: 10,
    type: 'ALL'
  }
  const { t } = useTranslation();

  // 从 Redux store 获取任务相关参数
  const dataClassificationState = useSelector((state: any) => state.dataClassification.dataClassificationState);

  const classResultOptions = Object.keys(CLASSIFICATION_RES).map(type => ({ label: CLASSIFICATION_RES[type as ClassificationRes], value: type }));

  const [tableParams, setTableParams] = React.useState<TableParams>(defaultTableParams);
  const [exportModalVisible, setExportModalVisible] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  //列表
  const { data: columnData, loading: listLoading, run: runGetColumnListList } = useRequest((params: TableParams) => {
    const { pageSize, pageNum, ...restParams } = params;

    // 构建API所需的参数，包含必需的任务ID字段
    const apiParams: IClassTaskExecuteTaskParams & any = {
      classifys_id: dataClassificationState?.classifys_id || 0,
      id: dataClassificationState?.id || 0,
      recon_id: dataClassificationState?.recon_id || 0,
      ...restParams,
      limit: getTablePaginatinLimitFiled(pageNum, pageSize)
    }

    return ClassResColumnAPI(apiParams);
  }, {
    manual: true,
    debounceInterval: 300,
    formatResult: (res: any) => {
      return {
        total: res?.count?.data?.[0]?.[0] || 0,
        list: res?.datas ?? []
      }
    }
  });

  //分级任务列表
  const {data: taskLevel } = useRequest(() => {
    return getClassDatasourceAPI({key: 'dd:data_dams_hit_tag_task_result_tree'});
  }, {
    formatResult: (res: any) => {
      const options = generateKeyValueObjects(res?.datas?.data, res?.datas?.index)
      return options;
    }
  })

  //导出
  const { run: runExportColumn } = useRequest('', {
    onSuccess: () => {
     message.success(t('common.message.export_success'))
     setExportModalVisible(false);
    }
  })

  useEffect(() => {
    runGetColumnListList({...tableParams})
  }, [tableParams, runGetColumnListList])
  //翻页多选
  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: selectedRowKeys,
    getCheckboxProps: (record: any) => ({
      disabled: record?.sourceType === 'AUTO_USER' // 自动授权禁用
    }),
    onSelectAll(selected, newSelectedRows: any[]) {
      const curRowKeys = newSelectedRows.map(row => row?.permissionId);

      let cloneSelectedRowKeys = _.cloneDeep(selectedRowKeys);
      if (selected) {
        cloneSelectedRowKeys = cloneSelectedRowKeys.concat(curRowKeys)
      } else {
        const curKeys = (columnData?.list || []).map((row: any) => row?.permissionId) || [];
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => !curKeys.includes(k))
      }
      setSelectedRowKeys([...new Set(cloneSelectedRowKeys)]);
    },
    onSelect(item, selected) {
      let cloneSelectedRowKeys = _.cloneDeep(selectedRowKeys);
      if (selected) {
        cloneSelectedRowKeys = cloneSelectedRowKeys.concat([item.permissionId])
      } else {
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => k !== item.permissionId)
      }
      setSelectedRowKeys(cloneSelectedRowKeys);
    },
  };

  return (
    <div className={styles.columnResultWrapper}>
      <div className={styles.header}>
        <div className={styles.left}>
          <Space>
            <Input.Search
              placeholder={t('classGrading.tab.result.column.searchPlac')}
              allowClear
              onSearch={(val: string) => setTableParams({ ...tableParams, pageNum: 1, search: val })}
              className={styles.searchInput}
            />
            <Select 
              allowClear 
              options={taskLevel} 
              placeholder={t('classGrading.tab.result.column.searchTask.plac')} 
              onChange={(val: string) => setTableParams({ ...tableParams,level_name: val, pageNum: 1 })}
            />
            <Select
              value={tableParams?.type}
              options={classResultOptions}
              onChange={(val: string) => setTableParams({ ...tableParams, type: val, pageNum: 1 })}
            />
          </Space>
        </div>
        <Button type='primary' onClick={() => setExportModalVisible(true)}>{t('common.btn.export')}</Button>
      </div>
      <div className={styles.content}>
        <ErrorBoundary>
          <Table
            loading={listLoading}
            className={styles.table}
            columns={columns()}
            dataSource={columnData?.list || []}
            scroll={{
              y: `calc(100vh - 450px)`,
              x: 'auto' // 添加横向滚动
            }}
            rowSelection={rowSelection}
            pagination={{
              showQuickJumper: true,
              showSizeChanger: true,
              pageSize: tableParams?.pageSize || 10,
              current: tableParams?.pageNum || 1,
              total: columnData?.total || 0,
              showTotal: (total) => t('common.table.pagination.total', { total }),
              onChange: (pageNumber, pageSize = 10) => {
                setTableParams({ ...tableParams, pageNum: pageNumber, pageSize });
              },
            }}
          />
        </ErrorBoundary>
      </div>
      {/* 导出 */}
      {
        exportModalVisible &&
        <ExportModal
          onExport={() => { runExportColumn() }}
          onCancel={() => setExportModalVisible(false)}
        />
      }
    </div>
  )
}

export default ColumnResultTab;

