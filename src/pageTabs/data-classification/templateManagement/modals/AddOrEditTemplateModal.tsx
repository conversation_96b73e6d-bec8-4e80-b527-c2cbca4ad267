import React, { useEffect } from 'react';
import { Form, Input, Select } from 'antd';
import type { FormInstance } from 'antd/es/form';
import { useTranslation } from 'react-i18next';
import { useRequest } from 'src/hook';
import {
  getClassDatasourceAPI
} from 'src/api';
import { UIModal } from 'src/components';
import { FormLayout } from '../../constants';
import { transformSelectOtionData } from '../../utils';

export default ({
  detailInfo,
  onCancel,
  onSubmit
}: {
  detailInfo: any;
  onCancel: () => void;
  onSubmit: (values: any) => void;
}) => {

  const { t } = useTranslation();
  const [templateForm] = Form.useForm();

  const { loading: submitBtnLoading, run: Update } = useRequest('', {

  })
  //查询标签关联类型
  const {data: tagList, run: runGetTagType } = useRequest(getClassDatasourceAPI, {
    manual: true,
    formatResult(res: any) {
      return transformSelectOtionData(res?.datas?.data || [])
    },
  })

   //查询关联分级分类列表
   const {data: classifyList, run: runGetClassifyType } = useRequest(getClassDatasourceAPI, {
    manual: true,
    formatResult(res: any) {
      return transformSelectOtionData(res?.datas?.data || [])
    },
  })

  useEffect(() => {
    runGetTagType({ key: 'dd:dams_label_type' });
    runGetClassifyType({key: 'dd:data_template_selectlv'})
  }, [])

  useEffect(() => {
    if (!templateForm) return;
    templateForm.setFieldsValue({
     ...detailInfo
    })
  }, [templateForm]);

  const onFormValidate = () => {
    templateForm.validateFields().then((values) => {
      // 字段映射：将表单字段名映射为API要求的字段名
      const mappedValues = {
        ...values,
        label_type: values.tagType?.toString(),
        level_type: values.relatedTemplate?.toString()
      };
      
      // 删除原始字段名
      delete mappedValues.tagType;
      delete mappedValues.relatedTemplate;

      onSubmit({
        ...(detailInfo || {}),
        ...mappedValues,
        tp_id: detailInfo?.tp_id,
       
        action: detailInfo ? 'edit_template' : 'create_template',
      })
    })
  };

  return (
    <UIModal
      title={detailInfo ? t('classGrading.tab.template.editTemplate') : t('classGrading.tab.template.addTemplate')}
      visible={true}
      onOk={onFormValidate}
      onCancel={onCancel}
      confirmLoading={submitBtnLoading}
      width={600}
    >
      <Form form={templateForm} {...FormLayout}>
        <Form.Item
          label={t('classGrading.tab.template.templateName.label')}
          name="tp_name"
          rules={[{ required: true, message: t('classGrading.tab.template.templateName.plac') }]}
        >
          <Input placeholder={t('classGrading.tab.template.templateName.plac')} />
        </Form.Item>

        <Form.Item 
          label={t('classGrading.tab.template.tagType.label')} 
          name="tagType"
          >
          <Select options={tagList} placeholder={t('classGrading.tab.template.tagType.plac')} allowClear />
        </Form.Item>

        <Form.Item 
          label={t('classGrading.tab.template.relatedTemplate.label')} 
          name="relatedTemplate"
          >
          <Select options={classifyList} placeholder={t('classGrading.tab.template.relatedTemplate.plac')} allowClear />
        </Form.Item>
      </Form>
    </UIModal>
  );
}
